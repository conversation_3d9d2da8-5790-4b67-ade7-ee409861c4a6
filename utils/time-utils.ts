import { addMinutes, differenceInMinutes, format, parse } from "date-fns";

/**
 * Parses a time string into a Date object
 * @param time - Time string in HH:mm, HH:mm:ss, or ISO format
 * @param baseDate - Base date to use for parsing (defaults to current date)
 * @returns Date object or null if parsing fails
 */
export const parseTime = (
  time: string | undefined,
  baseDate: Date = new Date()
): Date | null => {
  if (!time) return null;

  try {
    // Try parsing as ISO string first
    const iso = new Date(time);
    if (!isNaN(iso.getTime())) return iso;

    // Try parsing as HH:mm:ss
    const hhmmss = parse(time, "HH:mm:ss", baseDate);
    if (!isNaN(hhmmss.getTime())) return hhmmss;

    // Try parsing as HH:mm
    const hhmm = parse(time, "HH:mm", baseDate);
    if (!isNaN(hhmm.getTime())) return hhmm;
    return null;
  } catch {
    // If parsing fails, return null
    return null;
  }
};

/**
 * Creates a time label in HH:mm format from minutes offset from a base date
 * @param baseDate - The base date to calculate from
 * @param minsFromStart - Minutes offset from the base date
 * @returns Formatted time string in HH:mm format
 */
export const toLabel = (baseDate: Date, minsFromStart: number): string => {
  try {
    const date = addMinutes(baseDate ?? new Date(), minsFromStart);
    return format(date, "HH:mm");
  } catch {
    // If formatting fails, return a default time
    return "00:00";
  }
};

/**
 * Clamps a number between min and max values
 * @param n - Number to clamp
 * @param min - Minimum value
 * @param max - Maximum value
 * @returns Clamped number
 */
export const clamp = (n: number, min: number, max: number): number =>
  Math.max(min, Math.min(max, n));

/**
 * Calculates the difference in minutes between two dates
 * @param end - End date
 * @param start - Start date
 * @returns Difference in minutes
 */
export const getTimeWindowInMinutes = (end: Date, start: Date): number => {
  return differenceInMinutes(end, start);
};
