import React, { useState } from "react";
import { View } from "react-native";
import { TimeRangeFieldComponent } from "../shared/filter/time-ranger";
import { TimeRangeField } from "../shared/filter-component";
import { VStack } from "../ui/vstack";
import { Text } from "../ui/text";
import { Button, ButtonText } from "../ui/button";

// Test component to verify the refactored TimeRangeFieldComponent
export const TimeRangerTest = () => {
  const [timeRange, setTimeRange] = useState({
    startTime: "08:00",
    endTime: "18:00",
  });

  const testField: TimeRangeField = {
    type: "timeRange",
    key: "test-time-range",
    label: "Test Time Range",
    startTime: "06:00",
    endTime: "22:00",
    step: 30,
    minTime: 0,
    maxTime: 960, // 16 hours (6:00 AM to 10:00 PM)
  };

  const handleTimeRangeChange = (newValue: { startTime: string; endTime: string }) => {
    console.log("Time range changed:", newValue);
    setTimeRange(newValue);
  };

  const resetTimeRange = () => {
    setTimeRange({
      startTime: "06:00",
      endTime: "22:00",
    });
  };

  return (
    <VStack space="lg" className="p-4">
      <Text className="text-xl font-bold">Time Range Component Test</Text>
      
      <TimeRangeFieldComponent
        field={testField}
        value={timeRange}
        onChange={handleTimeRangeChange}
      />
      
      <VStack space="sm">
        <Text className="font-semibold">Current Values:</Text>
        <Text>Start Time: {timeRange.startTime}</Text>
        <Text>End Time: {timeRange.endTime}</Text>
      </VStack>
      
      <Button onPress={resetTimeRange}>
        <ButtonText>Reset to Default</ButtonText>
      </Button>
    </VStack>
  );
};

export default TimeRangerTest;
