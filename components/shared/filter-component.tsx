import React, { useState, useEffect } from "react";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Button, ButtonText } from "@/components/ui/button";
import { Icon } from "@/components/ui/icon";
import { Pressable } from "@/components/ui/pressable";
import { ScrollView } from "@/components/ui/scroll-view";
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
} from "@/components/ui/actionsheet";

import { X } from "lucide-react-native";
import { AvailableTimeSlot } from "@/data/screens/activities/types";

import { Setting4 } from "iconsax-react-nativejs";

import { Badge, BadgeText } from "../ui/badge";

// Filter field types
export interface SelectOption {
  label: string;
  value: string;
}

export interface MultiSelectField {
  type: "multiselect";
  key: string;
  label: string;
  options: SelectOption[];
  placeholder?: string;
}

export interface SingleSelectField {
  type: "select";
  key: string;
  label: string;
  options: SelectOption[];
  placeholder?: string;
}

export interface TimeField {
  type: "time";
  key: string;
  label: string;
  timeSlots: AvailableTimeSlot[];
  placeholder?: string;
}

export interface TimeRangeField {
  type: "timeRange";
  key: string;
  label: string;
  /**
   * If provided, the slider anchors to a specific date and uses minutes offset from this start.
   * Example: "2025-08-07T00:00:00" (ISO) or "00:00" when used with dayDate.
   */
  startTime?: string; // "HH:mm" or ISO string to anchor the range start
  endTime?: string; // "HH:mm" or ISO string to anchor the range end
  /**
   * Optional specific day used with HH:mm parsing. If omitted and startTime/endTime are HH:mm,
   * the current date is assumed.
   */
  dayDate?: Date;
  /**
   * Slider bounds in minutes from the anchored start (defaults to [0, 1440])
   */
  minTime?: number; // in minutes from anchored start (default 0)
  maxTime?: number; // in minutes from anchored start (default 1440)
  step?: number; // step in minutes (default: 30)
}

export interface DateField {
  type: "date";
  key: string;
  label: string;
  placeholder?: string;
}

export interface ToggleField {
  type: "toggle";
  key: string;
  label: string;
  description?: string;
}

export type FilterField =
  | MultiSelectField
  | SingleSelectField
  | TimeField
  | TimeRangeField
  | DateField
  | ToggleField;

export interface FilterValues {
  // Allow timeRange values to use the new consistent format
  [key: string]:
    | string
    | string[]
    | boolean
    | { startTime: string; endTime: string } // Updated to match new TimeRangeValue format
    | Date
    | null;
}

export interface FilterComponentProps {
  fields: FilterField[];
  values: FilterValues;
  onValuesChange: (values: FilterValues) => void;
  onApply: (values: FilterValues) => void;
  onReset: () => void;
  title?: string;
}

import MultiSelectFieldComponent from "./filter/multi-select-field";
import SingleSelectFieldComponent from "./filter/single-select-field";
import TimeFieldComponent from "./filter/time-field";
import DateFieldComponent from "./filter/date-field";
import ToggleFieldComponent from "./filter/toggle-field";
import { TimeRangeFieldComponent } from "./filter/time-ranger";

export const FilterComponent = ({
  fields,
  values,
  onValuesChange,
  onApply,
  onReset,
  title = "Filter",
}: FilterComponentProps) => {
  const [localValues, setLocalValues] = useState<FilterValues>(values);

  const [isOpen, setIsOpen] = useState(false);

  const activeFilterCount = 4;

  useEffect(() => {
    if (isOpen) {
      setLocalValues(values);
    }
  }, [values, isOpen]);

  const handleFieldChange = (
    key: string,
    value:
      | string
      | string[]
      | boolean
      | { startTime: string; endTime: string }
      | Date
      | null
  ) => {
    const newValues = { ...localValues, [key]: value };
    setLocalValues(newValues);
    onValuesChange(newValues);
  };

  const handleApply = () => {
    onApply?.(localValues);
    setIsOpen(false);
  };

  const handleReset = () => {
    const resetValues: FilterValues = {};
    fields.forEach((field) => {
      if (field.type === "multiselect") {
        resetValues[field.key] = [];
      } else if (field.type === "toggle") {
        resetValues[field.key] = false;
      } else if (field.type === "timeRange") {
        resetValues[field.key] = {
          startTime: "04:00", // Default start time (240 minutes = 4:00 AM)
          endTime: "24:00", // Default end time (1440 minutes = 24:00)
        };
      } else if (field.type === "date") {
        resetValues[field.key] = null;
      } else {
        resetValues[field.key] = "";
      }
    });
    setLocalValues(resetValues);
    onReset();
  };

  const renderField = (field: FilterField) => {
    const value = localValues[field.key];

    switch (field.type) {
      case "multiselect":
        return (
          <MultiSelectFieldComponent
            key={field.key}
            field={
              field as unknown as import("./filter/multi-select-field").MultiSelectField
            }
            value={(value as string[]) || []}
            onChange={(newValue) => handleFieldChange(field.key, newValue)}
          />
        );
      case "select":
        return (
          <SingleSelectFieldComponent
            key={field.key}
            field={
              field as unknown as import("./filter/single-select-field").SingleSelectField
            }
            value={(value as string) || ""}
            onChange={(newValue) => handleFieldChange(field.key, newValue)}
          />
        );
      case "time":
        return (
          <TimeFieldComponent
            key={field.key}
            field={field as unknown as import("./filter/time-field").TimeField}
            value={(value as string) || ""}
            onChange={(newValue) => handleFieldChange(field.key, newValue)}
          />
        );
      case "timeRange": {
        const trField = field as TimeRangeField;
        const timeRangeValue = value as
          | { startTime: string; endTime: string }
          | undefined;
        return (
          <TimeRangeFieldComponent
            key={trField.key}
            field={trField}
            value={
              timeRangeValue || {
                startTime: "04:00", // Default start time (240 minutes = 4:00 AM)
                endTime: "24:00", // Default end time (1440 minutes = 24:00)
              }
            }
            onChange={(newValue) => handleFieldChange(trField.key, newValue)}
          />
        );
      }
      case "date":
        return (
          <DateFieldComponent
            key={field.key}
            field={field as unknown as import("./filter/date-field").DateField}
            value={(value as Date) || null}
            onChange={(newValue) => handleFieldChange(field.key, newValue)}
          />
        );
      case "toggle":
        return (
          <ToggleFieldComponent
            key={field.key}
            field={
              field as unknown as import("./filter/toggle-field").ToggleField
            }
            value={(value as boolean) || false}
            onChange={(newValue) => handleFieldChange(field.key, newValue)}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
      <Pressable
        onPress={() => setIsOpen(true)}
        className={`w-10 h-10 rounded-full items-center justify-center border relative ${
          activeFilterCount > 0
            ? "bg-[#00BFE0] border-[#00BFE0]"
            : "bg-background-50 border-outline-200"
        }`}
      >
        <Icon
          color={activeFilterCount > 0 ? "white" : "gray"}
          as={() => (
            <Setting4
              size="20"
              color={activeFilterCount > 0 ? "white" : "gray"}
            />
          )}
          className={
            activeFilterCount > 0 ? "text-white" : "text-typography-600"
          }
          size="md"
        />

        {/* Filter count badge */}
        {activeFilterCount > 0 && (
          <Badge
            className="absolute -top-1 -right-1 bg-red-500 min-w-5 h-5 rounded-full items-center justify-center px-1"
            variant="solid"
          >
            <BadgeText className="text-white text-xs font-dm-sans-bold">
              {activeFilterCount > 99 ? "99+" : activeFilterCount.toString()}
            </BadgeText>
          </Badge>
        )}
      </Pressable>
      {isOpen && (
        <Actionsheet isOpen={isOpen} onClose={() => setIsOpen(false)}>
          <ActionsheetBackdrop className="bg-black opacity-50" />
          <ActionsheetContent className="bg-white flex-1 max-h-[85%]">
            <ActionsheetDragIndicatorWrapper>
              <ActionsheetDragIndicator />
            </ActionsheetDragIndicatorWrapper>

            {/* Header */}
            <HStack className="items-center  self-start mb-4">
              <Pressable onPress={() => setIsOpen(false)}>
                <Icon as={X} size="lg" className="text-typography-700 mr-6" />
              </Pressable>
              <Text className="text-xl font-dm-sans-bold text-typography-900 text-left">
                {title}
              </Text>
            </HStack>

            {/* Filter Fields */}
            <ScrollView
              className="flex-1 w-full"
              showsVerticalScrollIndicator={false}
              contentContainerClassName="flex-grow"
            >
              <VStack space="lg" className=" w-full">
                {fields.map(renderField)}
              </VStack>
            </ScrollView>

            {/* Footer Actions */}
            <HStack
              space="md"
              className="px-6 py-6 border-t border-outline-100 bg-white"
            >
              <Button
                variant="outline"
                onPress={handleReset}
                className="flex-1 border-typography-300 bg-transparent rounded-full h-12"
              >
                <ButtonText className="text-typography-600 font-dm-sans-medium text-base">
                  Reset all
                </ButtonText>
              </Button>
              <Button
                onPress={handleApply}
                className="flex-1 bg-[#00BFE0] rounded-full h-12"
              >
                <ButtonText className="text-black font-dm-sans-medium text-base">
                  Apply
                </ButtonText>
              </Button>
            </HStack>
          </ActionsheetContent>
        </Actionsheet>
      )}
    </>
  );
};

FilterComponent.displayName = "FilterComponent";
export default FilterComponent;
