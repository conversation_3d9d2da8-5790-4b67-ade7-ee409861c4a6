import React from "react";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";
import { Icon } from "@/components/ui/icon";
import { X } from "lucide-react-native";

export const RemovableTag = ({
  label,
  onRemove,
}: {
  label: string;
  onRemove: () => void;
}) => (
  <HStack className="bg-[#E6F9FC] border-[#00BFE0] border border-dashed rounded-md px-2 py-1 mr-1 mb-1 items-center">
    <Text className="text-[#00BFE0] font-dm-sans-medium text-xs mr-1">
      {label}
    </Text>
    <Pressable onPress={onRemove}>
      <Icon as={X} size="2xs" className="text-[#00BFE0]" />
    </Pressable>
  </HStack>
);

export default RemovableTag;
