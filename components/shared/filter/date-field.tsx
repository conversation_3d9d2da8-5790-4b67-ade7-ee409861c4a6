import React, { useState } from "react";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import { Pressable } from "@/components/ui/pressable";
import { Icon } from "@/components/ui/icon";
import { CalendarDays } from "lucide-react-native";
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
} from "@/components/ui/actionsheet";
import CalendarWidget from "../calendar-widget";

export interface DateField {
  type: "date";
  key: string;
  label: string;
  placeholder?: string;
}

const DateFieldComponent = ({
  field,
  value,
  onChange,
}: {
  field: DateField;
  value: Date | null;
  onChange: (value: Date | null) => void;
}) => {
  const [showDatePicker, setShowDatePicker] = useState(false);

  const handleDateSelect = (date: Date) => {
    onChange(date);
    setShowDatePicker(false);
  };

  return (
    <VStack space="sm" className="w-full">
      <Text className="text-typography-700 font-dm-sans-medium text-base">
        {field.label}
      </Text>

      <Pressable
        onPress={() => setShowDatePicker(true)}
        className="w-full bg-background-50 border border-outline-200 rounded-xl px-4 py-4 h-14 min-h-14 flex-row items-center justify-between"
      >
        <Text
          className={`flex-1 text-base ${value ? "text-typography-900" : "text-typography-500"}`}
        >
          {value
            ? value.toLocaleDateString()
            : field.placeholder || "Select date"}
        </Text>
        <Icon as={CalendarDays} size="sm" className="text-typography-500" />
      </Pressable>

      {showDatePicker && (
        <Actionsheet
          isOpen={showDatePicker}
          onClose={() => setShowDatePicker(false)}
        >
          <ActionsheetBackdrop />
          <ActionsheetContent>
            <ActionsheetDragIndicatorWrapper>
              <ActionsheetDragIndicator />
            </ActionsheetDragIndicatorWrapper>
            <VStack className="w-full">
              <CalendarWidget
                selectedDate={value || new Date()}
                onDateSelect={handleDateSelect}
              />
            </VStack>
          </ActionsheetContent>
        </Actionsheet>
      )}
    </VStack>
  );
};

export default DateFieldComponent;
