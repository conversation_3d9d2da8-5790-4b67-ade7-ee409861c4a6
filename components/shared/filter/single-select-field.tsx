import React from "react";
import { VStack } from "@/components/ui/vstack";
import { Text } from "@/components/ui/text";
import {
  Select,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectInput,
  SelectItem,
  SelectPortal,
  SelectScrollView,
  SelectTrigger,
} from "@/components/ui/select";
import { Icon } from "@/components/ui/icon";
import { ArrowDown2 } from "iconsax-react-nativejs";

export interface SelectOption {
  label: string;
  value: string;
}

export interface SingleSelectField {
  type: "select";
  key: string;
  label: string;
  options: SelectOption[];
  placeholder?: string;
}

const SingleSelectFieldComponent = ({
  field,
  value,
  onChange,
}: {
  field: SingleSelectField;
  value: string;
  onChange: (value: string) => void;
}) => (
  <VStack space="sm" className="w-full">
    <Text className="text-typography-700 font-dm-sans-medium text-base">
      {field.label}
    </Text>
    <Select selectedValue={value} onValueChange={onChange}>
      <SelectTrigger
        size="md"
        className="w-full self-stretch bg-background-50 border border-outline-200 rounded-xl px-4 py-4 h-14 min-h-14"
      >
        <SelectInput
          placeholder={
            field.placeholder || `Select ${field.label.toLowerCase()}`
          }
          className="text-typography-700 flex-1 text-base"
        />
        <Icon as={() => <ArrowDown2 size="20" color="#9CA3AF" />} />
      </SelectTrigger>
      <SelectPortal>
        <SelectBackdrop />
        <SelectContent>
          <SelectDragIndicatorWrapper>
            <SelectDragIndicator />
          </SelectDragIndicatorWrapper>
          <SelectScrollView>
            {field.options.map((option) => (
              <SelectItem
                key={option.value}
                label={option.label}
                value={option.value}
              />
            ))}
          </SelectScrollView>
        </SelectContent>
      </SelectPortal>
    </Select>
  </VStack>
);

export default SingleSelectFieldComponent;
