import { useMemo, useState } from "react";
import { TimeRangeField } from "../filter-component";
import { differenceInMinutes, addMinutes, format } from "date-fns";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import { RangeSlider } from "@react-native-assets/slider";
import { parseTime, clamp } from "@/utils/time-utils";

interface TimeRangeValue {
  startTime: string; // HH:mm format
  endTime: string; // HH:mm format
}

export const TimeRangeFieldComponent = ({
  field,
  value,
  onChange,
}: {
  field: TimeRangeField;
  value: TimeRangeValue;
  onChange: (value: TimeRangeValue) => void;
}) => {
  const step = field.step ?? 30;

  // Create base date for time calculations
  const baseDate = useMemo(() => {
    if (field.dayDate) return field.dayDate;
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return today;
  }, [field.dayDate]);

  // Calculate start anchor (default to start of day)
  const startAnchor = useMemo(() => {
    if (field.startTime) {
      const parsed = parseTime(field.startTime, baseDate);
      if (parsed) return parsed;
    }
    const start = new Date(baseDate);
    start.setHours(0, 0, 0, 0);
    return start;
  }, [field.startTime, baseDate]);

  // Calculate end anchor (default to end of day)
  const endAnchor = useMemo(() => {
    if (field.endTime) {
      const parsed = parseTime(field.endTime, baseDate);
      if (parsed) return parsed;
    }
    const end = new Date(baseDate);
    end.setHours(23, 59, 59, 999);
    return end;
  }, [field.endTime, baseDate]);

  // Calculate slider bounds in minutes
  const totalWindowMins = Math.max(
    step,
    differenceInMinutes(endAnchor, startAnchor)
  );
  const minValue = field.minTime ?? 0;
  const maxValue = field.maxTime ?? totalWindowMins;

  // Convert time string to minutes from start anchor
  const timeToMinutes = (timeStr: string): number => {
    const parsed = parseTime(timeStr, baseDate);
    if (!parsed) return minValue;
    return Math.max(
      minValue,
      Math.min(maxValue, differenceInMinutes(parsed, startAnchor))
    );
  };

  // Convert minutes from start anchor to time string
  const minutesToTime = (minutes: number): string => {
    const date = addMinutes(startAnchor, minutes);
    return format(date, "HH:mm");
  };

  // Snap value to step and clamp to bounds
  const snapToStep = (minutes: number): number => {
    const snapped = Math.round(minutes / step) * step;
    return clamp(snapped, minValue, maxValue);
  };

  // Initialize slider range from value prop
  const [sliderRange, setSliderRange] = useState<[number, number]>(() => {
    if (value?.startTime && value?.endTime) {
      const start = snapToStep(timeToMinutes(value.startTime));
      const end = snapToStep(timeToMinutes(value.endTime));
      return [Math.min(start, end), Math.max(start, end)];
    }
    return [minValue, maxValue];
  });

  // Handle slider value changes during dragging
  const handleSliderChange = (range: [number, number]) => {
    const snappedRange: [number, number] = [
      snapToStep(range[0]),
      snapToStep(range[1]),
    ];

    // Ensure proper order
    const orderedRange: [number, number] = [
      Math.min(snappedRange[0], snappedRange[1]),
      Math.max(snappedRange[0], snappedRange[1]),
    ];

    setSliderRange(orderedRange);

    // Convert to time format and call onChange
    const timeValue: TimeRangeValue = {
      startTime: minutesToTime(orderedRange[0]),
      endTime: minutesToTime(orderedRange[1]),
    };

    onChange(timeValue);
  };

  // Display current time range
  const displayStartTime = minutesToTime(sliderRange[0]);
  const displayEndTime = minutesToTime(sliderRange[1]);

  return (
    <VStack space="sm" className="w-full">
      <HStack className="items-center justify-between mb-2">
        <Text className="text-typography-700 font-dm-sans-medium text-base">
          {field.label}
        </Text>
        <Text className="text-typography-500 text-base">
          {`${displayStartTime} - ${displayEndTime}`}
        </Text>
      </HStack>
      <RangeSlider
        minimumValue={minValue}
        maximumValue={maxValue}
        step={step}
        range={sliderRange}
        onValueChange={handleSliderChange}
        onSlidingComplete={handleSliderChange}
        crossingAllowed={false}
        outboundColor="#d1d5db"
        inboundColor="#00AECC"
        thumbStyle={{ backgroundColor: "white" }}
        enabled={true}
        trackHeight={10}
        thumbSize={20}
      />
    </VStack>
  );
};
