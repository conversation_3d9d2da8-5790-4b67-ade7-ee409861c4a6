import React from "react";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import {
  Select,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectPortal,
  SelectScrollView,
  SelectTrigger,
} from "@/components/ui/select";
import RemovableTag from "./removable-tag";
import MultiSelectItem, { SelectOption } from "./multi-select-item";
import { Icon } from "@/components/ui/icon";
import { ArrowDown2 } from "iconsax-react-nativejs";

export interface MultiSelectField {
  type: "multiselect";
  key: string;
  label: string;
  options: SelectOption[];
  placeholder?: string;
}

const MultiSelectFieldComponent = ({
  field,
  value,
  onChange,
}: {
  field: MultiSelectField;
  value: string[];
  onChange: (value: string[]) => void;
}) => {
  const selectedOptions = field.options.filter((option) =>
    value.includes(option.value)
  );

  const handleOptionToggle = (optionValue: string) => {
    const newValue = value.includes(optionValue)
      ? value.filter((v) => v !== optionValue)
      : [...value, optionValue];
    onChange(newValue);
  };

  return (
    <VStack space="sm" className="w-full">
      <Text className="text-typography-700 font-dm-sans-medium text-base">
        {field.label}
      </Text>

      <Select
        key={value.join(",")}
        selectedValue=""
        onValueChange={(next) => {
          if (typeof next === "string") {
            handleOptionToggle(next);
          }
        }}
      >
        <SelectTrigger
          size="md"
          className="w-full self-stretch bg-background-50 border border-outline-200 rounded-xl px-4  h-14 min-h-14"
        >
          {selectedOptions.length > 0 ? (
            <HStack className="flex-1 flex-wrap">
              {selectedOptions.map((option) => (
                <RemovableTag
                  key={option.value}
                  label={option.label}
                  onRemove={() => handleOptionToggle(option.value)}
                />
              ))}
            </HStack>
          ) : (
            <Text className="text-typography-500 text-base flex-1">
              {field.placeholder || `Select ${field.label.toLowerCase()}`}
            </Text>
          )}
          <Icon as={() => <ArrowDown2 size="20" color="#9CA3AF" />} />
        </SelectTrigger>

        <SelectPortal>
          <SelectBackdrop />
          <SelectContent>
            <SelectDragIndicatorWrapper>
              <SelectDragIndicator />
            </SelectDragIndicatorWrapper>
            <SelectScrollView>
              {field.options.map((option) => (
                <MultiSelectItem
                  key={option.value}
                  option={option}
                  isSelected={value.includes(option.value)}
                  onToggle={() => handleOptionToggle(option.value)}
                />
              ))}
            </SelectScrollView>
          </SelectContent>
        </SelectPortal>
      </Select>
    </VStack>
  );
};

export default MultiSelectFieldComponent;
