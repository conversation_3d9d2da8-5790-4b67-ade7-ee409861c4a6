import { api } from "@/lib/api";
import { EventsApiResponse } from "../screens/events/types";

export const fetchEventsByDate = async (params: Record<string, string>) => {
  try {
    const urlParams = new URLSearchParams({
      ...params,
      // only_available_reservations: params.only_available_reservations
      //   ? "true"
      //   : "false",
    }).toString();

    console.log("urlParams", { urlParams, params });

    const response = await api
      .get<EventsApiResponse>(`events/reservations/list?${urlParams}`)
      .json();

    return response?.events || [];
  } catch (err) {
    throw new Error("Could not fetch events", err as Error);
  }
};
