import { useCallback, useMemo, useState } from "react";
import { matchSorter } from "match-sorter";
import { useEventsQuery } from "@/data/screens/events/queries/useEventsQuery";
import { format } from "date-fns";
import { useFilter } from "./useFilter";
import { FilterField } from "@/components/shared/filter-component";
import { useClientInfo } from "@/data/screens/common/queries/useClientConfig";
import { EventResponse } from "@/data/screens/events/types";

export const useEventsWithFilter = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDate, setSelectedDate] = useState(new Date());

  const { data: clientData } = useClientInfo();

  // Define filter fields for events
  const filterFields: FilterField[] = useMemo(
    () => [
      {
        type: "multiselect",
        key: "types",
        label: "Event type",
        options: [
          { label: "Virtual", value: "virtual" },
          { label: "Live", value: "live" },
          { label: "Paid", value: "paid" },
          { label: "Free", value: "free" },
        ],
      },
      {
        type: "select",
        key: "gym_ids",
        label: "Location",
        placeholder: "Select location",
        options:
          clientData?.facilities.map((facility) => ({
            label: facility.name,
            value: String(facility.id),
          })) || [],
      },
      {
        type: "date",
        key: "date",
        label: "Date",
        placeholder: "Select date",
      },
      {
        type: "timeRange",
        key: "time",
        label: "Available start times",
        placeholder: "Select date",
      },
      {
        type: "toggle",
        key: "only_available_reservations",
        label: "Show only events with available reservations",
      },
    ],
    [clientData?.facilities]
  );

  // Initialize filter hook
  const {
    filterValues,
    filterProps,
    hasActiveFilters,
    activeFilterCount,
    clearAllFilters,
  } = useFilter(filterFields);

  // Fetch events data
  const {
    data: eventsData = [],
    isLoading,
    error,
    refetch,
    isRefetching,
  } = useEventsQuery({
    month_year: format(selectedDate, "yyyy-MM"),
    // gym_ids:
    //   typeof filterValues?.gym_ids === "string"
    //     ? filterValues.gym_ids
    //     : undefined,
    // types: filterValues?.types,
    // only_available_reservations: filterValues?.only_available_reservations,
    // ...filterValues,
  });

  // Apply filtering
  const filteredData = useMemo(() => {
    let filtered = eventsData as EventResponse[];

    // Apply search term
    if (searchTerm) {
      filtered = matchSorter(filtered, searchTerm, {
        keys: ["name", "gym_name", "room_name", "description"],
      });
    }

    return filtered;
  }, [eventsData, searchTerm]);

  const handleDateChange = useCallback((date: Date) => {
    setSelectedDate(date);
  }, []);

  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
  }, []);

  const clearSearch = useCallback(() => {
    setSearchTerm("");
  }, []);

  return {
    // Data
    events: filteredData,
    isLoading,
    isRefetching,
    error,

    // UI State
    selectedDate,
    searchTerm,

    // Filter state
    filterValues,
    filterFields,
    hasActiveFilters,
    activeFilterCount,

    // Actions
    handleDateChange,
    handleSearch,
    setSearchTerm,
    clearSearch,
    refetch,
    clearAllFilters,

    // Filter props for components
    filterProps,
  };
};
