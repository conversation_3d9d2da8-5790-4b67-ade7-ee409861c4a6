import { useState, useCallback, useMemo } from "react";
import {
  FilterValues,
  FilterField,
} from "@/components/shared/filter-component";

export interface UseFilterOptions {
  initialValues?: FilterValues;
  onApply?: (values: FilterValues) => void;
  onReset?: () => void;
}

export const useFilter = (
  fields: FilterField[],
  options: UseFilterOptions = {}
) => {
  const { initialValues = {}, onApply, onReset } = options;

  // Initialize filter values based on field types
  const getInitialValues = useCallback(() => {
    const values: FilterValues = { ...initialValues };

    fields.forEach((field) => {
      if (!(field.key in values)) {
        switch (field.type) {
          case "multiselect":
            values[field.key] = [];
            break;
          case "toggle":
            values[field.key] = false;
            break;
          case "timeRange":
            values[field.key] = {
              startTime: "04:00",
              endTime: "24:00",
            };
            break;
          case "date":
            values[field.key] = null;
            break;
          default:
            values[field.key] = "";
            break;
        }
      }
    });

    return values;
  }, [fields, initialValues]);

  const [filterValues, setFilterValues] =
    useState<FilterValues>(getInitialValues);
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  const openFilter = useCallback(() => {
    setIsFilterOpen(true);
  }, []);

  const closeFilter = useCallback(() => {
    setIsFilterOpen(false);
  }, []);

  const handleFilterChange = useCallback((values: FilterValues) => {
    setFilterValues(values);
  }, []);

  const handleApply = useCallback(
    (values: FilterValues) => {
      setFilterValues(values);
      onApply?.(values);
      closeFilter();
    },
    [onApply, closeFilter]
  );

  const handleReset = useCallback(() => {
    const resetValues = getInitialValues();
    setFilterValues(resetValues);
    onReset?.();
  }, [getInitialValues, onReset]);

  // Check if a filter value is active (has meaningful data)
  const isFilterValueActive = useCallback((value: unknown) => {
    if (Array.isArray(value)) {
      return value.length > 0;
    }
    if (typeof value === "boolean") {
      return value;
    }
    if (typeof value === "string") {
      return value.trim() !== "";
    }
    if (typeof value === "object" && value !== null) {
      // Handle timeRange objects
      if ("startTime" in value && "endTime" in value) {
        const timeRange = value as { startTime: string; endTime: string };
        return timeRange.startTime !== "04:00" || timeRange.endTime !== "24:00";
      }
      return true; // Other objects are considered active
    }
    if (value instanceof Date) {
      return true; // Dates are considered active when set
    }
    return false;
  }, []);

  // Check if any filters are active
  const hasActiveFilters = useMemo(() => {
    return Object.values(filterValues).some((value) =>
      isFilterValueActive(value)
    );
  }, [filterValues, isFilterValueActive]);

  // Get count of active filters
  const activeFilterCount = useMemo(() => {
    let count = 0;
    Object.values(filterValues).forEach((value) => {
      if (isFilterValueActive(value)) {
        if (Array.isArray(value)) {
          count += value.length; // Count each selected item in multiselect
        } else {
          count += 1; // Count other active filters as 1
        }
      }
    });
    return count;
  }, [filterValues, isFilterValueActive]);

  // Clear all filters
  const clearAllFilters = useCallback(() => {
    handleReset();
  }, [handleReset]);

  return {
    // State
    filterValues,
    isFilterOpen,

    // Actions
    openFilter,
    closeFilter,
    handleFilterChange,
    handleApply,
    handleReset,
    clearAllFilters,

    // Computed values
    hasActiveFilters,
    activeFilterCount,

    // Filter props (ready to spread into FilterComponent)
    filterProps: {
      fields,
      values: filterValues,
      onValuesChange: handleFilterChange,
      onApply: handleApply,
      onReset: handleReset,
    },
  };
};
